import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../models/ticket_model.dart';
import '../../services/ticket_stats_service.dart';
import '../../services/showtime_service.dart';
import 'movie_ticket_detail_page.dart';
import '../../services/ticket_service.dart';

class TicketManagementPage extends StatefulWidget {
  const TicketManagementPage({Key? key}) : super(key: key);

  @override
  State<TicketManagementPage> createState() => _TicketManagementPageState();
}

class _TicketManagementPageState extends State<TicketManagementPage> {
  final TicketStatsService _ticketStatsService = TicketStatsService();
  final ShowtimeService _showtimeService = ShowtimeService();
  final RxList<MovieTicketStats> _movieStats = <MovieTicketStats>[].obs;
  final RxBool _isLoading = false.obs;
  final RxString _searchQuery = ''.obs;
  final Rx<Map<String, dynamic>> _overallStats = Rx<Map<String, dynamic>>({});
  final RxSet<int> _loadedMovieIds = <int>{}.obs;

  @override
  void initState() {
    super.initState();
    _loadMovieStats();
    _loadOverallStats();
  }

  Future<void> _loadMovieStats() async {
    try {
      _isLoading.value = true;
      _loadedMovieIds.clear(); // Clear loaded IDs when refreshing

      // Load full stats directly - more reliable than lazy loading
      final fullStats = await _ticketStatsService.getAllMovieTicketStats();
      _movieStats.value = fullStats;

      // Mark all movies as loaded
      _loadedMovieIds.addAll(fullStats.map((s) => s.movieId));

      print('Loaded ${fullStats.length} movie stats');
      for (final stat in fullStats) {
        print(
            'Movie: ${stat.movieTitle} - Available: ${stat.totalAvailableTickets}, Sold: ${stat.soldTickets}, Revenue: ${stat.totalRevenue}');
      }
    } catch (e) {
      print('Error loading movie stats: $e');
      Get.snackbar(
        'Lỗi',
        'Không thể tải danh sách phim: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.withOpacity(0.7),
        colorText: Colors.white,
      );
    } finally {
      _isLoading.value = false;
    }
  }

  // No longer needed since we load all stats at once
  // Future<void> _loadStatsForMovie(int movieId) async { ... }

  Future<void> _loadOverallStats() async {
    try {
      final stats = await _ticketStatsService.getOverallStats();
      _overallStats.value = stats;
    } catch (e) {
      // Handle error silently for overall stats
    }
  }

  List<MovieTicketStats> get _filteredMovieStats {
    var stats = _movieStats.toList();

    // Filter by search query
    if (_searchQuery.value.isNotEmpty) {
      final query = _searchQuery.value.toLowerCase();
      stats = stats
          .where((stat) => stat.movieTitle.toLowerCase().contains(query))
          .toList();
    }

    return stats;
  }

  void _refreshData() {
    _loadMovieStats();
    _loadOverallStats();
  }

  Future<void> _cleanupCancelledTickets() async {
    final confirmed = await Get.dialog<bool>(
      AlertDialog(
        backgroundColor: const Color(0xFF1A1A2E),
        title: Text(
          'Dọn dẹp ghế vé đã hủy',
          style: GoogleFonts.mulish(color: Colors.white),
        ),
        content: Text(
          'Thao tác này sẽ giải phóng tất cả ghế của các vé đã hủy trước đây.\n\nBạn có chắc chắn muốn tiếp tục?',
          style: GoogleFonts.mulish(color: Colors.white70),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: Text(
              'Hủy',
              style: GoogleFonts.mulish(color: Colors.grey),
            ),
          ),
          TextButton(
            onPressed: () => Get.back(result: true),
            child: Text(
              'Dọn dẹp',
              style: GoogleFonts.mulish(color: Colors.orange),
            ),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        Get.dialog(
          AlertDialog(
            backgroundColor: const Color(0xFF1A1A2E),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const CircularProgressIndicator(color: Colors.orange),
                const SizedBox(height: 16),
                Text(
                  'Đang dọn dẹp ghế...',
                  style: GoogleFonts.mulish(color: Colors.white),
                ),
              ],
            ),
          ),
          barrierDismissible: false,
        );

        await _ticketStatsService.cleanupCancelledTicketsSeats();

        Get.back(); // Close loading dialog

        Get.snackbar(
          'Thành công',
          'Đã dọn dẹp ghế của vé đã hủy',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green.withOpacity(0.7),
          colorText: Colors.white,
        );

        _refreshData(); // Refresh data after cleanup
      } catch (e) {
        Get.back(); // Close loading dialog

        Get.snackbar(
          'Lỗi',
          'Không thể dọn dẹp: $e',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red.withOpacity(0.7),
          colorText: Colors.white,
        );
      }
    }
  }

  Future<void> _fixDataConsistency() async {
    final confirmed = await Get.dialog<bool>(
      AlertDialog(
        backgroundColor: const Color(0xFF1A1A2E),
        title: Text(
          'Sửa chữa dữ liệu không nhất quán',
          style: GoogleFonts.mulish(color: Colors.white),
        ),
        content: Text(
          'Thao tác này sẽ sửa chữa số ghế có sẵn không đúng trong các suất chiếu.\n\nBạn có chắc chắn muốn tiếp tục?',
          style: GoogleFonts.mulish(color: Colors.white70),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: Text(
              'Hủy',
              style: GoogleFonts.mulish(color: Colors.grey),
            ),
          ),
          TextButton(
            onPressed: () => Get.back(result: true),
            child: Text(
              'Sửa chữa',
              style: GoogleFonts.mulish(color: Colors.blue),
            ),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        Get.dialog(
          AlertDialog(
            backgroundColor: const Color(0xFF1A1A2E),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const CircularProgressIndicator(color: Colors.blue),
                const SizedBox(height: 16),
                Text(
                  'Đang sửa chữa dữ liệu...',
                  style: GoogleFonts.mulish(color: Colors.white),
                ),
              ],
            ),
          ),
          barrierDismissible: false,
        );

        await _showtimeService.fixAllShowtimeDataConsistency();

        Get.back(); // Close loading dialog

        Get.snackbar(
          'Thành công',
          'Đã sửa chữa dữ liệu không nhất quán',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green.withOpacity(0.7),
          colorText: Colors.white,
        );

        _refreshData(); // Refresh data after fix
      } catch (e) {
        Get.back(); // Close loading dialog

        Get.snackbar(
          'Lỗi',
          'Không thể sửa chữa: $e',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red.withOpacity(0.7),
          colorText: Colors.white,
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Color(0xff2B5876),
              Color(0xff4E4376),
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Header
              Container(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    IconButton(
                      onPressed: () => Get.back(),
                      icon: const Icon(Icons.arrow_back, color: Colors.white),
                    ),
                    Expanded(
                      child: Text(
                        'Quản lý vé',
                        style: GoogleFonts.mulish(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ),
                    IconButton(
                      onPressed: _refreshData,
                      icon: const Icon(Icons.refresh, color: Colors.white),
                    ),
                    IconButton(
                      onPressed: _cleanupCancelledTickets,
                      icon: const Icon(Icons.cleaning_services,
                          color: Colors.orange),
                      tooltip: 'Dọn dẹp ghế của vé đã hủy',
                    ),
                    IconButton(
                      onPressed: _fixDataConsistency,
                      icon: const Icon(Icons.build, color: Colors.blue),
                      tooltip: 'Sửa chữa dữ liệu không nhất quán',
                    ),
                  ],
                ),
              ),

              // Overall stats
              Obx(() {
                final stats = _overallStats.value;
                if (stats.isNotEmpty) {
                  return Container(
                    margin: const EdgeInsets.symmetric(horizontal: 16),
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: Colors.white.withOpacity(0.2)),
                    ),
                    child: Column(
                      children: [
                        Text(
                          'Tổng quan hệ thống',
                          style: GoogleFonts.mulish(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'Tổng vé: số giao dịch mua vé • Ghế đã bán: tổng ghế được đặt',
                          style: GoogleFonts.mulish(
                            fontSize: 10,
                            color: Colors.white.withOpacity(0.6),
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 12),
                        Row(
                          children: [
                            Expanded(
                              child: _buildStatItem(
                                'Phim',
                                '${stats['totalMovies'] ?? 0}',
                                Icons.movie,
                              ),
                            ),
                            Expanded(
                              child: _buildStatItem(
                                'Tổng vé',
                                '${stats['totalTickets'] ?? 0}',
                                Icons.confirmation_number,
                              ),
                            ),
                            Expanded(
                              child: _buildStatItem(
                                'Ghế đã bán',
                                '${stats['totalSoldTickets'] ?? 0}',
                                Icons.event_seat,
                              ),
                            ),
                            Expanded(
                              child: _buildStatItem(
                                'Doanh thu',
                                '${(stats['totalRevenue'] ?? 0.0).toStringAsFixed(0)} VNĐ',
                                Icons.attach_money,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  );
                }
                return const SizedBox.shrink();
              }),

              const SizedBox(height: 16),

              // Search bar
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: TextField(
                  onChanged: (value) => _searchQuery.value = value,
                  style: const TextStyle(color: Colors.white),
                  decoration: InputDecoration(
                    hintText: 'Tìm kiếm theo tên phim...',
                    hintStyle: TextStyle(color: Colors.white.withOpacity(0.7)),
                    prefixIcon: const Icon(Icons.search, color: Colors.white),
                    filled: true,
                    fillColor: Colors.white.withOpacity(0.1),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide.none,
                    ),
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Movie stats list
              Expanded(
                child: Obx(() {
                  if (_isLoading.value) {
                    return const Center(
                      child: CircularProgressIndicator(color: Colors.white),
                    );
                  }

                  final filteredStats = _filteredMovieStats;

                  if (filteredStats.isEmpty) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.movie_outlined,
                            size: 64,
                            color: Colors.white.withOpacity(0.5),
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'Không có phim nào',
                            style: GoogleFonts.mulish(
                              fontSize: 18,
                              color: Colors.white.withOpacity(0.7),
                            ),
                          ),
                        ],
                      ),
                    );
                  }

                  return ListView.builder(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    itemCount: filteredStats.length,
                    itemBuilder: (context, index) {
                      final movieStats = filteredStats[index];
                      return _buildMovieStatsCard(movieStats);
                    },
                  );
                }),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(
          icon,
          color: Colors.white.withOpacity(0.8),
          size: 20,
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: GoogleFonts.mulish(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        Text(
          label,
          style: GoogleFonts.mulish(
            fontSize: 10,
            color: Colors.white.withOpacity(0.7),
          ),
        ),
      ],
    );
  }

  Widget _buildMovieStatsCard(MovieTicketStats movieStats) {
    return GestureDetector(
      onTap: () {
        Get.to(() => MovieTicketDetailPage(
              movieId: movieStats.movieId,
              movieTitle: movieStats.movieTitle,
            ));
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: 12),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.white.withOpacity(0.2)),
        ),
        child: Row(
          children: [
            // Movie poster
            ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Image.network(
                movieStats.fullPosterPath,
                width: 60,
                height: 90,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    width: 60,
                    height: 90,
                    color: Colors.grey[800],
                    child: const Icon(
                      Icons.movie,
                      color: Colors.white54,
                      size: 30,
                    ),
                  );
                },
              ),
            ),
            const SizedBox(width: 16),

            // Movie info and stats
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Movie title
                  Text(
                    movieStats.movieTitle,
                    style: GoogleFonts.mulish(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 12),

                  // Stats row - no lazy loading needed since all stats are loaded upfront
                  Column(
                    children: [
                      // Stats row
                      Row(
                        children: [
                          Expanded(
                            child: _buildTicketStatItem(
                              'Ghế có sẵn',
                              '${movieStats.totalAvailableTickets}',
                              Colors.blue[300]!,
                            ),
                          ),
                          Expanded(
                            child: _buildTicketStatItem(
                              'Ghế đã bán',
                              '${movieStats.soldTickets}',
                              Colors.green[300]!,
                            ),
                          ),
                          Expanded(
                            child: _buildTicketStatItem(
                              'Ghế còn lại',
                              '${movieStats.remainingTickets}',
                              Colors.orange[300]!,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),

                      // Revenue and occupancy
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              'Doanh thu: ${movieStats.totalRevenue.toStringAsFixed(0)} VNĐ',
                              style: GoogleFonts.mulish(
                                fontSize: 12,
                                color: Colors.amber[300],
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                          Text(
                            '${movieStats.occupancyRate.toStringAsFixed(1)}%',
                            style: GoogleFonts.mulish(
                              fontSize: 12,
                              color: Colors.white.withOpacity(0.7),
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // Arrow icon
            const Icon(
              Icons.arrow_forward_ios,
              color: Colors.white54,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTicketStatItem(String label, String value, Color color) {
    return Column(
      children: [
        Text(
          value,
          style: GoogleFonts.mulish(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: GoogleFonts.mulish(
            fontSize: 10,
            color: Colors.white.withOpacity(0.7),
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }
}
