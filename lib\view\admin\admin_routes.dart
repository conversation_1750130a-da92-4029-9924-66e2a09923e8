import 'package:get/get.dart';
import 'admin_home_page.dart';
import 'admin_login_page.dart';
import 'banner_list_page.dart';
import 'banner_edit_page.dart';
import 'user_management_page.dart';
import 'notification_management_page.dart';
import 'ticket_management_page.dart';
import 'ticket_expiration_management_page.dart';
import 'test_functions_page.dart';
import 'theater_management_page.dart';
import 'schedule_management_page.dart';

class AdminRoutes {
  static final routes = [
    GetPage(
      name: '/admin',
      page: () => const AdminLoginPage(),
    ),
    GetPage(
      name: '/admin/home',
      page: () => const AdminHomePage(),
    ),
    GetPage(
      name: '/admin/banners',
      page: () => const BannerListPage(),
    ),
    GetPage(
      name: '/admin/banners/edit',
      page: () => const BannerEditPage(),
    ),
    GetPage(
      name: '/admin/users',
      page: () => const UserManagementPage(),
    ),
    GetPage(
      name: '/admin/notifications',
      page: () => const NotificationManagementPage(),
    ),
    GetPage(
      name: '/admin/tickets',
      page: () => const TicketManagementPage(),
    ),
    GetPage(
      name: '/admin/ticket-expiration',
      page: () => const TicketExpirationManagementPage(),
    ),
    GetPage(
      name: '/admin/test-functions',
      page: () => const TestFunctionsPage(),
    ),
    GetPage(
      name: '/admin/theaters',
      page: () => const TheaterManagementPage(),
    ),
    GetPage(
      name: '/admin/schedule',
      page: () => const ScheduleManagementPage(),
    ),
  ];
}
