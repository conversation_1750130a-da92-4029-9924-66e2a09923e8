import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/ticket_model.dart';
import '../models/movie_model.dart';
import '../models/showtime_model.dart';
import '../models/screen_model.dart';
import '../services/firebase_movie_service.dart';
import '../services/showtime_service.dart';
import '../services/screen_service.dart';

class TicketStatsService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseMovieService _movieService = FirebaseMovieService();
  final ShowtimeService _showtimeService = ShowtimeService();
  final ScreenService _screenService = ScreenService();

  // Get ticket statistics for all movies (optimized)
  Future<List<MovieTicketStats>> getAllMovieTicketStats() async {
    try {
      // Get all active movies and all tickets in parallel
      final futures = await Future.wait([
        _movieService.getMovies(),
        _getAllTickets(),
        _getAllShowtimesWithCapacity(),
      ]);

      final movies = futures[0] as List<Movie>;
      final allTickets = futures[1] as List<Ticket>;
      final showtimeCapacities = futures[2] as Map<int, int>;

      // Group tickets by movieId
      final ticketsByMovie = <int, List<Ticket>>{};
      for (final ticket in allTickets) {
        ticketsByMovie.putIfAbsent(ticket.movieId, () => []).add(ticket);
      }

      // Create stats for each movie
      final List<MovieTicketStats> statsList = [];
      for (final movie in movies) {
        final movieTickets = ticketsByMovie[movie.id] ?? [];
        final totalCapacity = showtimeCapacities[movie.id] ?? 0;

        final stats = MovieTicketStats.fromData(
          movieId: movie.id,
          movieTitle: movie.title,
          moviePosterPath: movie.posterPath,
          totalAvailableTickets: totalCapacity,
          tickets: movieTickets,
        );
        statsList.add(stats);
      }

      // Sort by movie title
      statsList.sort((a, b) => a.movieTitle.compareTo(b.movieTitle));
      return statsList;
    } catch (e) {
      throw Exception('Failed to get movie ticket stats: $e');
    }
  }

  // Get all tickets at once
  Future<List<Ticket>> _getAllTickets() async {
    try {
      final snapshot = await _firestore.collection('tickets').get();
      final tickets =
          snapshot.docs.map((doc) => Ticket.fromFirestore(doc)).toList();

      print('Total tickets in database: ${tickets.length}');

      // Debug: Count tickets by status
      final statusCounts = <String, int>{};
      for (final ticket in tickets) {
        statusCounts[ticket.status.name] =
            (statusCounts[ticket.status.name] ?? 0) + 1;
      }
      print('Tickets by status: $statusCounts');

      return tickets;
    } catch (e) {
      throw Exception('Failed to get all tickets: $e');
    }
  }

  // Get all showtimes with their capacities (optimized)
  Future<Map<int, int>> _getAllShowtimesWithCapacity() async {
    try {
      // Get all showtimes and screens in parallel - include inactive screens for accurate count
      final futures = await Future.wait([
        _showtimeService.getAllShowtimes(),
        _screenService.getAllScreens(activeOnly: false), // Include all screens
      ]);

      final showtimes = futures[0] as List<ShowtimeModel>;
      final screens = futures[1] as List<ScreenModel>;

      // Create a map of screenId -> totalSeats for quick lookup
      final screenCapacities = <String, int>{};
      for (final screen in screens) {
        screenCapacities[screen.id] = screen.totalSeats;
      }

      // Group by movieId and sum capacities
      final capacities = <int, int>{};
      for (final showtime in showtimes) {
        final screenCapacity = screenCapacities[showtime.screenId] ?? 0;
        if (screenCapacity > 0) {
          // Only count if screen exists and has seats
          capacities[showtime.movieId] =
              (capacities[showtime.movieId] ?? 0) + screenCapacity;
        }
      }

      return capacities;
    } catch (e) {
      print('Error getting showtime capacities: $e');
      // If we can't get capacities, return empty map
      return {};
    }
  }

  // Get basic movie list with lazy loading stats
  Future<List<MovieTicketStats>> getMovieListWithLazyStats() async {
    try {
      // First, get just the movies quickly
      final movies = await _movieService.getMovies();

      // Return basic stats with zero values, will be updated later
      return movies
          .map((movie) => MovieTicketStats(
                movieId: movie.id,
                movieTitle: movie.title,
                moviePosterPath: movie.posterPath,
                totalAvailableTickets: 0,
                soldTickets: 0,
                remainingTickets: 0,
                totalRevenue: 0.0,
                tickets: [],
              ))
          .toList();
    } catch (e) {
      throw Exception('Failed to get movie list: $e');
    }
  }

  // Update stats for a specific movie (for lazy loading)
  Future<MovieTicketStats?> updateMovieStats(
      MovieTicketStats movieStats) async {
    try {
      final updatedStats = await getMovieTicketStats(movieStats.movieId);
      return updatedStats;
    } catch (e) {
      // Return original stats if update fails
      return movieStats;
    }
  }

  // Get ticket statistics for a specific movie
  Future<MovieTicketStats?> getMovieTicketStats(int movieId) async {
    try {
      // Get movie details
      final movie = await _movieService.getMovieByMovieId(movieId);
      if (movie == null) return null;

      // Get all tickets for this movie
      final ticketsSnapshot = await _firestore
          .collection('tickets')
          .where('movieId', isEqualTo: movieId)
          .get();

      final tickets =
          ticketsSnapshot.docs.map((doc) => Ticket.fromFirestore(doc)).toList();

      // Calculate total available tickets for this movie
      final totalAvailableTickets =
          await _calculateTotalAvailableTickets(movieId);

      return MovieTicketStats.fromData(
        movieId: movieId,
        movieTitle: movie.title,
        moviePosterPath: movie.posterPath,
        totalAvailableTickets: totalAvailableTickets,
        tickets: tickets,
      );
    } catch (e) {
      throw Exception(
          'Failed to get movie ticket stats for movie $movieId: $e');
    }
  }

  // Calculate total available tickets for a movie based on showtimes and screens
  Future<int> _calculateTotalAvailableTickets(int movieId) async {
    try {
      // Get all showtimes for this movie
      final showtimes = await _showtimeService.getShowtimesByMovie(movieId);
      int totalTickets = 0;

      for (final showtime in showtimes) {
        // Get screen details to know total seats
        final screen = await _screenService.getScreenById(showtime.screenId);
        if (screen != null && screen.totalSeats > 0) {
          totalTickets += screen.totalSeats;
        }
      }

      print(
          'Movie $movieId: ${showtimes.length} showtimes, $totalTickets total seats');
      return totalTickets;
    } catch (e) {
      print('Error calculating total available tickets for movie $movieId: $e');
      // If we can't calculate, return 0
      return 0;
    }
  }

  // Get tickets for a specific movie
  Future<List<Ticket>> getMovieTickets(int movieId) async {
    try {
      final snapshot = await _firestore
          .collection('tickets')
          .where('movieId', isEqualTo: movieId)
          .orderBy('purchaseDate', descending: true)
          .get();

      return snapshot.docs.map((doc) => Ticket.fromFirestore(doc)).toList();
    } catch (e) {
      throw Exception('Failed to get movie tickets: $e');
    }
  }

  // Get tickets for a specific movie with user information
  Future<List<Map<String, dynamic>>> getMovieTicketsWithUserInfo(
      int movieId) async {
    try {
      final tickets = await getMovieTickets(movieId);
      final List<Map<String, dynamic>> ticketsWithUserInfo = [];

      for (final ticket in tickets) {
        // Get user information
        String userName = 'Unknown User';
        String userEmail = '';

        try {
          final userDoc =
              await _firestore.collection('users').doc(ticket.userId).get();

          if (userDoc.exists) {
            final userData = userDoc.data() as Map<String, dynamic>;
            userName =
                userData['displayName'] ?? userData['name'] ?? 'Unknown User';
            userEmail = userData['email'] ?? '';
          }
        } catch (e) {
          // If we can't get user info, use default
        }

        ticketsWithUserInfo.add({
          'ticket': ticket,
          'userName': userName,
          'userEmail': userEmail,
        });
      }

      return ticketsWithUserInfo;
    } catch (e) {
      throw Exception('Failed to get movie tickets with user info: $e');
    }
  }

  // Cancel a ticket
  Future<void> cancelTicket(String ticketId, {double? refundAmount}) async {
    try {
      await _firestore.collection('tickets').doc(ticketId).update({
        'status': TicketStatus.cancelled.name,
        'cancelledAt': Timestamp.now(),
        'refundAmount': refundAmount ?? 0.0,
      });
    } catch (e) {
      throw Exception('Failed to cancel ticket: $e');
    }
  }

  // Get overall statistics
  Future<Map<String, dynamic>> getOverallStats() async {
    try {
      final allStats = await getAllMovieTicketStats();

      int totalMovies = allStats.length;
      int totalAvailableSeats = allStats.fold(
          0, (total, stats) => total + stats.totalAvailableTickets);
      int totalSoldSeats =
          allStats.fold(0, (total, stats) => total + stats.soldTickets);
      int totalRemainingSeats =
          allStats.fold(0, (total, stats) => total + stats.remainingTickets);
      double totalRevenue =
          allStats.fold(0.0, (total, stats) => total + stats.totalRevenue);

      // Calculate total tickets (transactions) across all movies
      int totalTickets =
          allStats.fold(0, (total, stats) => total + stats.totalTickets);

      double overallOccupancyRate = totalAvailableSeats > 0
          ? (totalSoldSeats / totalAvailableSeats) * 100
          : 0.0;

      return {
        'totalMovies': totalMovies,
        'totalAvailableTickets':
            totalAvailableSeats, // Keep key name for compatibility
        'totalSoldTickets': totalSoldSeats, // Now represents seats sold
        'totalRemainingTickets': totalRemainingSeats,
        'totalTickets': totalTickets, // Total number of ticket transactions
        'totalRevenue': totalRevenue,
        'overallOccupancyRate': overallOccupancyRate,
      };
    } catch (e) {
      throw Exception('Failed to get overall stats: $e');
    }
  }

  // Stream for real-time updates
  Stream<List<MovieTicketStats>> getAllMovieTicketStatsStream() {
    return _firestore
        .collection('tickets')
        .snapshots()
        .asyncMap((_) => getAllMovieTicketStats());
  }
}
